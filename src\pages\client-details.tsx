"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import {
    AlertTriangle,
    ArrowLeft,
    Calendar,
    CheckCircle,
    CreditCard,
    DollarSign,
    FileText,
    Shield,
    TrendingDown,
    TrendingUp,
    User,
} from "lucide-react"
import { Link, useParams } from "react-router-dom"

// Mock client data - in a real app, this would come from an API
const mockClientDetails = {
    "1": {
        id: 1,
        name: "SME 1",
        sector: "Retail",
        years: 5,
        account_balance: 50000,
        inflow: 3500,
        outflow: 2000,
        status: "Active",
        registrationDate: "2024-01-15",
        email: "<EMAIL>",
        phone: "+965 1234 5678",
        address: "123 Business Street, Kuwait City, Kuwait",
        description: "A thriving retail business specializing in consumer electronics and accessories.",
        inflowOutflowRatio: 1.75,
        averageBalance6Months: 42500,
        minimumBalance3Months: 22500,
        estimatedCollateralValue: 60000,
        collateralToLoanRatio: 1.5,
        monthsSinceVATFiling: 2,
        avgPosVolume3Months: 3260,
        posGrowthRate3Months: 12.5,
        chargebackRate: 0.2,
        recentTransactions: [
            { id: 1, date: "2025-01-20", type: "Inflow", amount: 3200, description: "Sales Revenue" },
            { id: 2, date: "2025-01-19", type: "Outflow", amount: 1800, description: "Inventory Purchase" },
            { id: 3, date: "2025-01-18", type: "Inflow", amount: 2800, description: "Sales Revenue" },
            { id: 4, date: "2025-01-17", type: "Outflow", amount: 1200, description: "Rent Payment" },
            { id: 5, date: "2025-01-16", type: "Inflow", amount: 4100, description: "Sales Revenue" },
        ],
        loanHistory: [
            {
                id: 1,
                amount: "KWD 25,000",
                status: "Approved",
                date: "2024-06-15",
                purpose: "Inventory Expansion",
                currentUtilization: 85.2,
                repaidRatio: 14.8,
            },
            {
                id: 2,
                amount: "KWD 15,000",
                status: "Completed",
                date: "2024-02-10",
                purpose: "Equipment Purchase",
                currentUtilization: 0,
                repaidRatio: 100,
            },
        ],
        repaymentMissed12Months: 1,
        financeUsed12Months: 40000,
        posData: [
            {
                id: 1,
                date: "2025-01-20",
                terminalId: "POS-001",
                transactionCount: 45,
                volume: 3200,
                averageTransaction: 71.11,
                chargeback: 0.15,
            },
            {
                id: 2,
                date: "2025-01-19",
                terminalId: "POS-001",
                transactionCount: 38,
                volume: 2800,
                averageTransaction: 73.68,
                chargeback: 0.12,
            },
            {
                id: 3,
                date: "2025-01-18",
                terminalId: "POS-002",
                transactionCount: 52,
                volume: 4100,
                averageTransaction: 78.85,
                chargeback: 0.18,
            },
            {
                id: 4,
                date: "2025-01-17",
                terminalId: "POS-001",
                transactionCount: 41,
                volume: 2950,
                averageTransaction: 71.95,
                chargeback: 0.14,
            },
            {
                id: 5,
                date: "2025-01-16",
                terminalId: "POS-002",
                transactionCount: 47,
                volume: 3400,
                averageTransaction: 72.34,
                chargeback: 0.16,
            },
        ],
    },
    "2": {
        id: 2,
        name: "SME 2",
        sector: "Restaurant",
        years: 6,
        account_balance: 20000,
        inflow: 2000,
        outflow: 1500,
        status: "Active",
        registrationDate: "2024-03-20",
        email: "<EMAIL>",
        phone: "+965 2345 6789",
        address: "456 Food Court, Salmiya, Kuwait",
        description: "Family-owned restaurant serving traditional Middle Eastern cuisine.",
        inflowOutflowRatio: 1.33,
        averageBalance6Months: 17000,
        minimumBalance3Months: 9000,
        estimatedCollateralValue: 24000,
        collateralToLoanRatio: 2.4,
        monthsSinceVATFiling: 3,
        avgPosVolume3Months: 1850,
        posGrowthRate3Months: 8.2,
        chargebackRate: 0.3,
        recentTransactions: [
            { id: 1, date: "2025-01-20", type: "Inflow", amount: 1800, description: "Daily Sales" },
            { id: 2, date: "2025-01-19", type: "Outflow", amount: 1200, description: "Food Supplies" },
            { id: 3, date: "2025-01-18", type: "Inflow", amount: 2200, description: "Daily Sales" },
            { id: 4, date: "2025-01-17", type: "Outflow", amount: 800, description: "Staff Wages" },
            { id: 5, date: "2025-01-16", type: "Inflow", amount: 1900, description: "Daily Sales" },
        ],
        loanHistory: [
            {
                id: 1,
                amount: "KWD 10,000",
                status: "Approved",
                date: "2024-11-01",
                purpose: "Kitchen Equipment",
                currentUtilization: 65.5,
                repaidRatio: 34.5,
            },
        ],
        repaymentMissed12Months: 0,
        financeUsed12Months: 10000,
        posData: [
            {
                id: 1,
                date: "2025-01-20",
                terminalId: "POS-R01",
                transactionCount: 32,
                volume: 1800,
                averageTransaction: 56.25,
                chargeback: 0.25,
            },
            {
                id: 2,
                date: "2025-01-19",
                terminalId: "POS-R01",
                transactionCount: 28,
                volume: 1600,
                averageTransaction: 57.14,
                chargeback: 0.22,
            },
            {
                id: 3,
                date: "2025-01-18",
                terminalId: "POS-R01",
                transactionCount: 35,
                volume: 2200,
                averageTransaction: 62.86,
                chargeback: 0.28,
            },
            {
                id: 4,
                date: "2025-01-17",
                terminalId: "POS-R01",
                transactionCount: 30,
                volume: 1750,
                averageTransaction: 58.33,
                chargeback: 0.24,
            },
            {
                id: 5,
                date: "2025-01-16",
                terminalId: "POS-R01",
                transactionCount: 33,
                volume: 1900,
                averageTransaction: 57.58,
                chargeback: 0.26,
            },
        ],
    },
    "3": {
        id: 3,
        name: "SME 3",
        sector: "Entertainment",
        years: 3,
        account_balance: 40000,
        inflow: 2500,
        outflow: 2000,
        status: "Active",
        registrationDate: "2025-06-06",
        email: "<EMAIL>",
        phone: "+965 3456 7890",
        address: "789 Entertainment District, Hawalli, Kuwait",
        description: "Event planning and entertainment services company.",
        inflowOutflowRatio: 1.25,
        averageBalance6Months: 34000,
        minimumBalance3Months: 18000,
        estimatedCollateralValue: 48000,
        collateralToLoanRatio: 0.4,
        monthsSinceVATFiling: 8,
        avgPosVolume3Months: 2460,
        posGrowthRate3Months: -10.5,
        chargebackRate: 0.8,
        recentTransactions: [
            { id: 1, date: "2025-01-20", type: "Inflow", amount: 3000, description: "Event Booking" },
            { id: 2, date: "2025-01-19", type: "Outflow", amount: 2200, description: "Equipment Rental" },
            { id: 3, date: "2025-01-18", type: "Inflow", amount: 2800, description: "Event Booking" },
            { id: 4, date: "2025-01-17", type: "Outflow", amount: 1500, description: "Staff Payment" },
            { id: 5, date: "2025-01-16", type: "Inflow", amount: 2200, description: "Event Booking" },
        ],
        loanHistory: [
            {
                id: 1,
                amount: "KWD 120,000",
                status: "Rejected",
                date: "2025-07-03",
                purpose: "Business Expansion",
                currentUtilization: 0,
                repaidRatio: 0,
            },
            {
                id: 2,
                amount: "KWD 30,000",
                status: "Completed",
                date: "2024-12-15",
                purpose: "Equipment Purchase",
                currentUtilization: 0,
                repaidRatio: 100,
            },
        ],
        repaymentMissed12Months: 3,
        financeUsed12Months: 30000,
        posData: [
            {
                id: 1,
                date: "2025-01-20",
                terminalId: "POS-E01",
                transactionCount: 15,
                volume: 3000,
                averageTransaction: 200.0,
                chargeback: 0.65,
            },
            {
                id: 2,
                date: "2025-01-19",
                terminalId: "POS-E02",
                transactionCount: 12,
                volume: 2400,
                averageTransaction: 200.0,
                chargeback: 0.58,
            },
            {
                id: 3,
                date: "2025-01-18",
                terminalId: "POS-E01",
                transactionCount: 14,
                volume: 2800,
                averageTransaction: 200.0,
                chargeback: 0.62,
            },
            {
                id: 4,
                date: "2025-01-17",
                terminalId: "POS-E01",
                transactionCount: 10,
                volume: 2000,
                averageTransaction: 200.0,
                chargeback: 0.55,
            },
            {
                id: 5,
                date: "2025-01-16",
                terminalId: "POS-E02",
                transactionCount: 11,
                volume: 2200,
                averageTransaction: 200.0,
                chargeback: 0.6,
            },
        ],
    },
}

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case "active":
            return "bg-green-100 text-green-800"
        case "inactive":
            return "bg-gray-100 text-gray-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

const getLoanStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case "approved":
            return "bg-green-100 text-green-800"
        case "completed":
            return "bg-blue-100 text-blue-800"
        case "rejected":
            return "bg-red-100 text-red-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

export default function ClientDetails() {
    const params = useParams()
    const clientId = params.id

    const client = clientId ? mockClientDetails[clientId as keyof typeof mockClientDetails] : null

    if (!client) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Client Not Found</h2>
                    <p className="text-gray-600 mb-4">The requested client could not be found.</p>
                    <Link to="/clients">
                        <Button>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Clients
                        </Button>
                    </Link>
                </div>
            </div>
        )
    }

    return (
        <div>
            {/* Header */}
            <div className="mb-6">
                <div className="flex items-center gap-4 mb-4">
                    <Link to="/clients">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Clients
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{client.name}</h1>
                        <p className="text-gray-600">Client Details & Financial Overview</p>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Client Information */}
                <div className="lg:col-span-1">
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <User className="w-5 h-5" />
                                Client Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                            <div>
                                <label className="text-sm font-medium text-gray-600">Business Name</label>
                                <p className="text-gray-900 font-medium">{client.name}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Sector</label>
                                <p className="text-gray-900">{client.sector}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Years in Business</label>
                                <p className="text-gray-900">{client.years} years</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Status</label>
                                <div className="mt-1">
                                    <Badge variant="secondary" className={getStatusColor(client.status)}>
                                        {client.status}
                                    </Badge>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Registration Date</label>
                                <p className="text-gray-900">{client.registrationDate}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Risk & Compliance Information */}
                    <Card className="border border-gray-200">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="w-5 h-5" />
                                Risk & Compliance
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                            <div className="flex items-center gap-3">
                                <Shield className="w-4 h-4 text-gray-400" />
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Collateral to Loan Ratio</label>
                                    <p className="text-gray-900 font-bold">
                                        {client.collateralToLoanRatio === null ? "N/A" : client.collateralToLoanRatio}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {client.collateralToLoanRatio === null
                                            ? "No active loans"
                                            : client.collateralToLoanRatio >= 1.5
                                                ? "Well secured"
                                                : "Requires attention"}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <Calendar className="w-4 h-4 text-gray-400" />
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Months Since Last VAT Filing</label>
                                    <p className="text-gray-900 font-bold">{client.monthsSinceVATFiling} months</p>
                                    <p className="text-xs text-gray-500">
                                        {client.monthsSinceVATFiling <= 3
                                            ? "Compliant"
                                            : client.monthsSinceVATFiling <= 6
                                                ? "Attention needed"
                                                : "High risk"}
                                    </p>
                                </div>
                            </div>
                            {/*
                            <div>
                                <label className="text-sm font-medium text-gray-600">Description</label>
                                <p className="text-gray-700 text-sm mt-1">{client.description}</p>
                            </div>
                            */}
                        </CardContent>
                    </Card>

                    {/* Contact Information - Commented out */}
                    {/*
          <Card className="border border-gray-200">
            <CardHeader className="border-b border-gray-200">
              <CardTitle className="flex items-center gap-2">
                <Phone className="w-5 h-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className="text-gray-900">{client.email}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <p className="text-gray-900">{client.phone}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-400 mt-1" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Address</label>
                  <p className="text-gray-900">{client.address}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Description</label>
                <p className="text-gray-700 text-sm mt-1">{client.description}</p>
              </div>
            </CardContent>
          </Card>
          */}
                </div>

                {/* Financial Overview & Transactions */}
                <div className="lg:col-span-2">
                    {/* Financial Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Current Balance</p>
                                        <p className="text-2xl font-bold text-gray-900">${client.account_balance.toLocaleString()}</p>
                                    </div>
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <DollarSign className="text-blue-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Avg Balance (6 Months)</p>
                                        <p className="text-2xl font-bold text-green-600">
                                            ${client.averageBalance6Months.toLocaleString()}
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <TrendingUp className="text-green-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Min Balance (3 Months)</p>
                                        <p className="text-2xl font-bold text-amber-600">
                                            ${client.minimumBalance3Months.toLocaleString()}
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                                        <TrendingDown className="text-amber-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Financial Ratio with Inflow/Outflow */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Financial Analysis
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Inflow/Outflow Ratio */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Inflow/Outflow Ratio</p>
                                        <p className="text-3xl font-bold text-gray-900">{client.inflowOutflowRatio}</p>
                                        <p className="text-sm text-gray-500 mt-1">
                                            {Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0
                                                ? "Positive cash flow"
                                                : "Negative cash flow"}
                                        </p>
                                    </div>
                                    <div
                                        className={`w-16 h-16 ${Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0 ? "bg-green-100" : "bg-red-100"} rounded-lg flex items-center justify-center`}
                                    >
                                        {Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0 ? (
                                            <TrendingUp className="text-green-600 w-8 h-8" />
                                        ) : (
                                            <TrendingDown className="text-red-600 w-8 h-8" />
                                        )}
                                    </div>
                                </div>

                                {/* Monthly Inflow */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Monthly Inflow</p>
                                        <p className="text-2xl font-bold text-green-600">${client.inflow.toLocaleString()}</p>
                                        <p className="text-sm text-gray-500 mt-1">Average monthly income</p>
                                    </div>
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <TrendingUp className="text-green-600 w-6 h-6" />
                                    </div>
                                </div>

                                {/* Monthly Outflow */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Monthly Outflow</p>
                                        <p className="text-2xl font-bold text-red-600">${client.outflow.toLocaleString()}</p>
                                        <p className="text-sm text-gray-500 mt-1">Average monthly expenses</p>
                                    </div>
                                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                        <TrendingDown className="text-red-600 w-6 h-6" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* POS Data */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Point of Sales Data
                            </CardTitle>
                        </CardHeader>

                        {/* POS Metrics */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Avg POS Volume (3 Months)</p>
                                            <p className="text-2xl font-bold text-blue-900">${client.avgPosVolume3Months.toLocaleString()}</p>
                                        </div>
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <TrendingUp className="text-blue-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-green-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-green-600">POS Growth Rate (3 Months)</p>
                                            <p
                                                className={`text-2xl font-bold ${
                                                    client.posGrowthRate3Months >= 0 ? "text-green-900" : "text-red-900"
                                                }`}
                                            >
                                                {`${client.posGrowthRate3Months >= 0 ? "+" : ""}${client.posGrowthRate3Months.toFixed(1)}%`}
                                            </p>
                                        </div>
                                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                            <TrendingUp className="text-green-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-purple-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-purple-600">Chargeback Rate</p>
                                            <p className="text-2xl font-bold text-purple-900">{`${client.chargebackRate.toFixed(1)}%`}</p>
                                        </div>
                                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <CreditCard className="text-purple-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Terminal ID
                                        </th> */}
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Transaction Count
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Volume
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Average Transaction
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Chargeback %
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.posData.map((pos) => (
                                        <tr key={pos.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.date}</td>
                                            {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.terminalId}</td> */}
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.transactionCount}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                ${pos.volume.toLocaleString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${pos.averageTransaction.toFixed(2)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {pos.chargeback.toFixed(2)}%
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Transactions */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="w-5 h-5" />
                                Recent Transactions
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Type
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.recentTransactions.map((transaction) => (
                                        <tr key={transaction.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Badge
                                                    variant="secondary"
                                                    className={
                                                        transaction.type === "Inflow" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                                                    }
                                                >
                                                    {transaction.type}
                                                </Badge>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                ${transaction.amount.toLocaleString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.description}</td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Loan History */}
                    <Card className="border border-gray-200">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                LoanHistory 
                            </CardTitle>
                        </CardHeader>

                        {/* Loan Metrics Cards */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="bg-red-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-red-600">Repayment Missed (Last 12 Months)</p>
                                            <p className="text-2xl font-bold text-red-900">{client.repaymentMissed12Months}</p>
                                            <p className="text-sm text-gray-500 mt-1">
                                                {client.repaymentMissed12Months === 0
                                                    ? "Perfect record"
                                                    : client.repaymentMissed12Months <= 2
                                                        ? "Acceptable"
                                                        : "High risk"}
                                            </p>
                                        </div>
                                        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                            <AlertTriangle className="text-red-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Finance Used (Last 12 Months)</p>
                                            <p className="text-2xl font-bold text-blue-900">
                                                KWD {client.financeUsed12Months.toLocaleString()}
                                            </p>
                                            <p className="text-sm text-gray-500 mt-1">Total financing utilized</p>
                                        </div>
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <DollarSign className="text-blue-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Purpose
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Current Utilization
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Repaid Ratio
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.loanHistory.map((loan) => (
                                        <tr key={loan.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{loan.date}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{loan.amount}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Badge variant="secondary" className={getLoanStatusColor(loan.status)}>
                                                    {loan.status}
                                                </Badge>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{loan.purpose}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center">
                            <span
                                className={`font-medium ${loan.currentUtilization > 80 ? "text-red-600" : loan.currentUtilization > 50 ? "text-amber-600" : "text-green-600"}`}
                            >
                              {loan.currentUtilization.toFixed(1)}%
                            </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center">
                            <span
                                className={`font-medium ${loan.repaidRatio === 100 ? "text-green-600" : loan.repaidRatio > 50 ? "text-blue-600" : "text-amber-600"}`}
                            >
                              {loan.repaidRatio.toFixed(1)}%
                            </span>
                                                    {loan.repaidRatio === 100 && <CheckCircle className="w-4 h-4 text-green-600 ml-2" />}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
