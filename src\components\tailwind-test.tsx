export default function TailwindTest() {
  return (
    <div className="p-8 max-w-md mx-auto bg-white rounded-xl shadow-lg space-y-4">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900">Tailwind CSS Test</h1>
        <p className="text-gray-500">If you can see this styled correctly, <PERSON><PERSON>wind is working!</p>
      </div>
      
      <div className="space-y-2">
        <button className="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
          Primary Button
        </button>
        
        <button className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded transition-colors">
          Secondary Button
        </button>
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        <div className="h-12 bg-red-500 rounded"></div>
        <div className="h-12 bg-green-500 rounded"></div>
        <div className="h-12 bg-blue-500 rounded"></div>
      </div>
      
      <div className="text-sm text-gray-600">
        <p>✅ Colors working</p>
        <p>✅ Spacing working</p>
        <p>✅ Typography working</p>
        <p>✅ Layout working</p>
        <p>✅ Hover effects working</p>
      </div>
    </div>
  )
}
