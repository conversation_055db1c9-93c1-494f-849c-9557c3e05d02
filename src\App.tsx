import MainLayout from "@/components/layout/main-layout"
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"
import ClientDetails from "@/pages/client-details"
import Clients from "@/pages/clients"
import Dashboard from "@/pages/dashboard"
import NotFound from "@/pages/not-found"
import Requests from "@/pages/requests"
import { QueryClientProvider } from "@tanstack/react-query"
import { BrowserRouter, Route, Routes } from "react-router-dom"
import { queryClient } from "./lib/queryClient"

function Router() {
    return (
        <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/clients" element={<Clients />} />
            <Route path="/clients/:id" element={<ClientDetails />} />
            <Route path="/requests" element={<Requests />} />
            <Route path="*" element={<NotFound />} />
        </Routes>
    )
}

function App() {
    return (
        <BrowserRouter>
            <QueryClientProvider client={queryClient}>
                <TooltipProvider>
                    <Toaster />
                    <MainLayout>
                        <Router />
                    </MainLayout>
                </TooltipProvider>
            </QueryClientProvider>
        </BrowserRouter>
    )
}

export default App
